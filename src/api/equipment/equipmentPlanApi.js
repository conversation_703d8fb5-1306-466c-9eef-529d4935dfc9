import ycCsApi from "@/api/ycCsApi";

// 进口设备计划书相关接口
export const insertEquipmentPlan = (params) => window.majesty.httpUtil.postAction(ycCsApi.equipment.bizIEquipmentPlanHead.insert, params)
export const updateEquipmentPlan = (sid, params) => window.majesty.httpUtil.putAction(`${ycCsApi.equipment.bizIEquipmentPlanHead.update}/${sid}`, params)
export const deleteEquipmentPlan = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.equipment.bizIEquipmentPlanHead.delete}/${sids}`)
export const getEquipmentPlanList = (params) => window.majesty.httpUtil.postAction(ycCsApi.equipment.bizIEquipmentPlanHead.list, params)
export const exportEquipmentPlan = (params) => window.majesty.httpUtil.postAction(ycCsApi.equipment.bizIEquipmentPlanHead.export, params)
export const getEquipmentPlanForSelect = (params) => window.majesty.httpUtil.postAction(ycCsApi.equipment.bizIEquipmentPlanHead.listForPlan, params)

// 设备计划操作相关接口
export const confirmEquipmentPlan = (sid) => window.majesty.httpUtil.postAction(`${ycCsApi.equipment.bizIEquipmentPlanHead.confirm}/${sid}`)
export const sendAuditEquipmentPlan = (sids) => window.majesty.httpUtil.postAction(`${ycCsApi.equipment.bizIEquipmentPlanHead.sendAudit}/${sids}`)
export const invalidateEquipmentPlan = (sids) => window.majesty.httpUtil.postAction(`${ycCsApi.equipment.bizIEquipmentPlanHead.invalidate}/${sids}`)
export const copyVersionEquipmentPlan = (params) => window.majesty.httpUtil.postAction(`${ycCsApi.equipment.bizIEquipmentPlanHead.copyVersion}`, params)
export const checkEquipmentPlanNotCancel = (params) => window.majesty.httpUtil.postAction(`${ycCsApi.equipment.bizIEquipmentPlanHead.checkNotCancel}`, params)

// 设备计划划款通知相关接口
export const getEquipmentPlanPayNotifyList = (params) => window.majesty.httpUtil.postAction(ycCsApi.equipment.bizIEquipmentPlanPayNotify.list, params)
export const insertEquipmentPlanPayNotify = (params) => window.majesty.httpUtil.postAction(ycCsApi.equipment.bizIEquipmentPlanPayNotify.insert, params)
export const updateEquipmentPlanPayNotify = (sid, params) => window.majesty.httpUtil.putAction(`${ycCsApi.equipment.bizIEquipmentPlanPayNotify.update}/${sid}`, params)
export const deleteEquipmentPlanPayNotify = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.equipment.bizIEquipmentPlanPayNotify.delete}/${sids}`)

// 获取划款参数信息接口
export const getTransMes = (headId, businessType) => window.majesty.httpUtil.postAction(`${ycCsApi.equipment.bizIEquipmentPlanPayNotify.getTransMes}/${headId}/${businessType}`)

// 获取箱单列表接口
export const getContainerList = (businessType) => window.majesty.httpUtil.postAction(`${ycCsApi.equipment.bizIEquipmentPlanPayNotify.getContainerList}/${businessType}`, {})

// 获取单价合计信息接口
export const getContainerMes = (businessType, containerType) => window.majesty.httpUtil.postAction(`${ycCsApi.equipment.bizIEquipmentPlanPayNotify.getContainerMes}/${businessType}/${containerType}`)

// 设备计划箱型详情相关接口
export const getEquipmentContainerInfoList = (params) => window.majesty.httpUtil.postAction(ycCsApi.equipment.bizIEquipmentContainerInfo.list, params)
export const insertEquipmentContainerInfo = (params) => window.majesty.httpUtil.postAction(ycCsApi.equipment.bizIEquipmentContainerInfo.insert, params)
export const updateEquipmentContainerInfo = (sid, params) => window.majesty.httpUtil.putAction(`${ycCsApi.equipment.bizIEquipmentContainerInfo.update}/${sid}`, params)
export const deleteEquipmentContainerInfo = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.equipment.bizIEquipmentContainerInfo.delete}/${sids}`)
