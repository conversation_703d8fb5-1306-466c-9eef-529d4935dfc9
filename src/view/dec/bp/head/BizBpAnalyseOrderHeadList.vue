<template>
  <!-- （第7条线）出料加工进口薄片-分析单表头 -->
  <section class="dc-section" v-if="true">
    <div class="cs-action" v-show="show">
      <!-- 查询列表区域 -->
      <div class="cs-search">
        <a-card :bordered="false">
          <bread-crumb>
            <div ref="area_head">
              <div class="search-btn">
                <a-button size="small" type="primary" class="cs-margin-right cs-refresh" @click="handlerRefresh"
                          v-show="showSearch">
                  <template #icon>
                    <GlobalIcon type="redo" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" class="cs-margin-right" @click="handlerSearch">
                  查询
                  <template #icon>
                    <GlobalIcon type="search" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" danger class="cs-margin-right cs-warning"
                          @click="handleShowSearch">
                  <template #icon>
                    <GlobalIcon v-show="!showSearch" type="down" style="color:#fff"/>
                    <GlobalIcon v-show="showSearch" type="up" style="color:#fff"/>
                  </template>
                </a-button>
              </div>
            </div>
          </bread-crumb>
          <div class="separateLine"></div>
          <!-- 查询按钮组件 -->
          <div ref="area_search">
            <div v-show="showSearch">
              <biz-bp-analyse-order-head-search ref="headSearch" ></biz-bp-analyse-order-head-search>
            </div>
          </div>
        </a-card>
      </div>
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
        <div class="cs-action-btn-item" v-has="['yc-cs:iEBusiness:analyse:add']">
          <a-button size="small" @click="handlerAdd">
            <template #icon>
              <GlobalIcon type="plus" style="color:green"/>
            </template>
            {{ localeContent('m.common.button.add') }}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:iEBusiness:analyse:edit']">
          <a-button size="small" @click="handlerEdit">
            <template #icon>
              <GlobalIcon type="form" style="color:orange"/>
            </template>
            {{ localeContent('m.common.button.update') }}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:iEBusiness:analyse:delete']">
          <a-button size="small" :loading="deleteLoading" @click="handlerDelete">
            <template #icon>
              <GlobalIcon type="delete" style="color:red"/>
            </template>
            {{ localeContent('m.common.button.delete') }}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:iEBusiness:analyse:export']">
          <a-button size="small" :loading="exportLoading" @click="handlerExport">
            <template #icon>
              <GlobalIcon type="folder-open" style="color:orange"/>
            </template>
            {{ localeContent('m.common.button.export') }}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:iEBusiness:analyse:confirm']">
          <a-button size="small" :loading="confirmLoading" @click="handleConfirm">
            <template #icon>
              <GlobalIcon type="check" style="color:red"/>
            </template>
            确认
          </a-button>
        </div>

        <!-- 打印分析单 -->
        <div class="cs-action-btn-item" v-has="['yc-cs:iEBusiness:analyse:print']">
          <a-button size="small" :loading="printAnalysisLoading" @click="handlePrintAnalysis">
            <template #icon>
              <GlobalIcon type="printer" style="color:blue"/>
            </template>
            打印分析单
          </a-button>
        </div>

        <!-- 打印装运通知 -->
        <div class="cs-action-btn-item" v-has="['yc-cs:iEBusiness:analyse:print']">
          <a-button size="small" :loading="printShipmentLoading" @click="handlePrintShipment">
            <template #icon>
              <GlobalIcon type="printer" style="color:green"/>
            </template>
            打印装运通知
          </a-button>
        </div>

        <!-- 打印装船电 -->
        <div class="cs-action-btn-item" v-has="['yc-cs:iEBusiness:analyse:print']">
          <a-button size="small" :loading="printShippingLoading" @click="handlePrintShipping">
            <template #icon>
              <GlobalIcon type="printer" style="color:orange"/>
            </template>
            打印装船电
          </a-button>
        </div>


        <div class="cs-action-btn-settings">
          <!-- 自定义显示组件 -->
          <CsTableColSettings
            v-if="totalColumns.length > 0"
            :resId="tableKey"
            :tableKey="tableKey+'-smoke_machine_code'"
            :initSettingColumns="totalColumns"
            :showColumnSettings="true"
            @customColumnChange="customColumnChange"
          >
          </CsTableColSettings>
        </div>


      </div>

      <!-- 表格区域 -->
      <div>
        <s-table
          ref="tableRef"
          class="cs-action-item  remove-table-border-add-bg"
          size="small"
          :scroll="{ y: tableHeight,x:400 }"
          bordered
          :pagination="false"
          :columns="showColumns.length > 0 ?showColumns:totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="id"
          :custom-row="customRow"
          column-drag
          :row-height="30"
          :row-hover-delay="5"
          :header-height="30"
          :range-selection="false"
        >
          <!-- 空数据 -->
          <template #emptyText>
            <a-empty description="暂无数据" />
          </template>
          <!-- 操作 -->
          <template #bodyCell="{ column,record }">
            <template v-if="column.key === 'operation'">
              <div class="operation-container">
                <div>
                <a-button
                  size="small"
                  type="link"
                  @click="handleEditByRow(record)"
                  :style="operationEdit('edit')">

                    <template #icon>
                      <GlobalIcon type="form" style="color:#e93f41"/>
                    </template>
                </a-button>
                </div>


                <div >
                <a-button
                  size="small"
                  type="link"
                  @click="handleViewByRow(record)"
                  :style="operationEdit('view')"
                >
                  <template #icon>
                    <GlobalIcon type="search" style="color:#1677ff"/>
                  </template>
                </a-button>
                </div>
              </div>
            </template>
          </template>
        </s-table>
      </div>

      <!-- 分页 -->
      <div class=cs-pagination>
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer :page-size="page.pageSize"
                      :total="page.total" @change="onPageChange">
          <template #buildOptionText="props">
            <span>{{ props.value }}条/页</span>
          </template>
        </a-pagination>

      </div>
    </div>

    <!-- 新增 编辑数据 -->
    <div v-if="!show">
        <bp-analyse-tab :edit-config="editConfig" @onEditBack="handlerOnBack"/>
    </div>



    <!-- 新增合同号 -->
    <cs-modal :visible="isShowExtract" title="新增进货单" :width="1000" :footer="false" @cancel="handlerBackExtract">
      <template #customContent>

        <div v-if="addLoading"
             style="width: 100%;height: 58vh;display: flex;justify-content: center;align-items: center">
          <a-spin tip="新增中..."/>
        </div>
        <add-bp-analyse-dialog v-else ref="contractModalRef" @cancel="handlerBackExtract"
                                  @save="handlerExtract"></add-bp-analyse-dialog>
      </template>
    </cs-modal>



  </section>
  <section v-else>
      <biz-bp-add-box-info></biz-bp-add-box-info>
  </section>


</template>

<script setup>
  /* 使用自定义 Hook 函数 */
  import {useCommon} from '@/view/common/useCommon'
  import {createVNode, h, onMounted, reactive, ref} from "vue";
  import {message, Modal, Tag} from "ant-design-vue";
  import BreadCrumb from "@/components/breadcrumb/BreadCrumb.vue";
  import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
  import {deleteClient} from "@/api/bi/bi_client_info";
  import {localeContent} from "@/view/utils/commonUtil";
  import ycCsApi from "@/api/ycCsApi";
  import CsTableColSettings from "@/components/settings/CsTableColSettings.vue";
  import {useRoute} from "vue-router";
  import {editStatus, productClassify} from "@/view/common/constant";
  import BpAnalyseTab from "@/view/dec/bp/BpAnalyseTab.vue";
  import BizBpAnalyseOrderHeadSearch from "@/view/dec/bp/head/BizBpAnalyseOrderHeadSearch.vue";
  import BizBpAddBoxInfo from "@/view/dec/bp/componment/BizBpAddBoxInfo.vue";
  import {useColumnsRender} from "@/view/common/useColumnsRender";
  import CsModal from "@/components/modal/cs-modal.vue";
  import AddSmokeMachineDialog from "@/view/dec/smoke_machine/componment/AddSmokeMachineDialog.vue";
  import AddBpAnalyseDialog from "@/view/dec/bp/componment/AddBpAnalyseDialog.vue";
  const { cmbShowRender } = useColumnsRender()


  /* 引入通用方法 */
  const {
    editConfig,
    show,
    page,
    showSearch,
    headSearch,
    handleEditByRow,
    handleViewByRow,
    operationEdit,
    onPageChange,
    handleShowSearch,
    handlerSearch,
    dataSourceList,
    tableLoading,
    getTableScroll,
    exportLoading,
    getList,
    ajaxUrl,
    doExport,
    handlerRefresh,
    customRow

  } = useCommon()


  /* 定义分析单表头组件名称 */
  defineOptions({
    name: 'BizBpAnalyseOrderHeadList',
  });



  /* 字段信息 */
  /* table表格字段属性设置 */
  const totalColumns = ref([
    {
      width: 150,
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
    },
    {
      title: '合同号',
      width: 200,
      align: 'center',
      dataIndex: 'contractNo',
      key: 'contractNo',
    },
    {
      title: '分析单号',
      width: 200,
      align: 'center',
      dataIndex: 'analysisNo',
      key: 'analysisNo',
    },
    {
      title: '客户',
      width: 200,
      align: 'center',
      dataIndex: 'customerCode',
      key: 'customerCode',
      customRender: ({ text }) => {
        return cmbShowRender(text,customerList.value)
      }
    },
    {
      title: '币种',
      width: 200,
      align: 'center',
      dataIndex: 'curr',
      key: 'curr',
      customRender: ({ text }) => {
        return cmbShowRender(text,currList.value)
      }
    },

    {
      title: '金额',
      width: 200,
      align: 'center',
      dataIndex: 'total',
      key: 'total',
    },
    // 装运期限
    {
      title: '装运期限',
      width: 200,
      align: 'center',
      dataIndex: 'shipmentDeadline',
      key: 'shipmentDeadline',
    },
    {
      title: '制单人',
      width: 200,
      align: 'center',
      dataIndex: 'createBy',
      key: 'createBy',
    },
    {
      title: '制单时间',
      width: 200,
      align: 'center',
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: '单据状态',
      width: 200,
      align: 'center',
      dataIndex: 'dataState',
      key: 'dataState',
      customRender: ({ text }) => {
        const colors = ['green', 'blue', 'red','black'];
        return h(Tag,{
          color: colors[parseInt(text)],
          size: 'small',
        }, cmbShowRender(text,productClassify.orderIncomingStatus))
      },
    },
    {
      title: '确认时间',
      width: 200,
      align: 'center',
      dataIndex: 'confirmTime',
      key: 'confirmTime',
    }



  ])


  const isShowExtract = ref(false)
  const addLoading = ref(false)



  const tableHeight = ref('')

  /* 引入表单数据 */
  const gridData = reactive({
    selectedRowKeys: [],
    selectedData: [],
    loading: false,
  });


  /* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
  const onSelectChange = (selectedRowKeys, rowSelectData) => {
    gridData.selectedData = rowSelectData;
    gridData.selectedRowKeys = selectedRowKeys;
  };


  /* 按钮loading */
  const deleteLoading = ref(false)
  const printAnalysisLoading = ref(false)
  const printShipmentLoading = ref(false)
  const printShippingLoading = ref(false)


  /* 返回事件 */
  const handlerOnBack = (flag) => {
    show.value = !show.value;
    // 返回清空选择数据
    gridData.selectedData = [];
    gridData.selectedRowKeys = [];
    editConfig.editData = {}
    getList()
  }

  /* 新增数据 */
  const handlerAdd = () => {
    // editConfig.value.editStatus = editStatus.ADD
    // show.value = !show.value;
    isShowExtract.value = true
  }


  /* 编辑数据 */
  const handlerEdit = () => {
    if (gridData.selectedRowKeys.length <= 0) {
      message.warning('请选择一条数据')
      return
    }
    if (gridData.selectedRowKeys.length > 1) {
      message.warning('只能选择一条数据')
      return
    }
    editConfig.value.editStatus = editStatus.EDIT
    editConfig.value.editData = gridData.selectedData[0]

    show.value = !show.value;
  }


  /* 删除数据 */
  const handlerDelete = () => {
    if (gridData.selectedRowKeys.length <= 0) {
      message.warning('请选择一条数据')
      return
    }
    if (gridData.selectedRowKeys.length > 1) {
      message.warning('只能选择一条数据')
      return
    }
    // 非编制数据不能删除
    if (gridData.selectedData[0].dataState !== '0') {
      message.warning('只有编制状态的数据允许删除')
      return
    }
    // 弹出确认框
    Modal.confirm({
      title: '提醒?',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '删除',
      cancelText: '取消',
      content: '确认删除所选项吗？',
      onOk() {
        deleteLoading.value = true
        window.majesty.httpUtil.deleteAction(`${ycCsApi.bizBpAnalyseOrderHead.delete}/${gridData.selectedRowKeys[0]}`).then(res => {
          if (res.code === 200) {
            message.success("删除成功！")
            getList()
          }else {
            message.error(res.message)
          }
        }).finally(() => {
          deleteLoading.value = false
        })
      },
      onCancel() {

      },
    });

  }



  /* 导出事件 */
  const handlerExport = () => {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const hours = String(now.getHours()).padStart(2, '0')
    const minutes = String(now.getMinutes()).padStart(2, '0')
    const seconds = String(now.getSeconds()).padStart(2, '0')
    const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`
    doExport(`进口订单表头${timestamp}.xlsx`, totalColumns)
  }


  /* 自定义设置 */
  /* 显示列数据 */
  const showColumns = ref([])

  /* 唯一键 */
  const tableKey = ref('')
  tableKey.value = window.$vueApp ? window.majesty.router.patch : useRoute().path

  /* 选中visible为true的数据进行显示 */
  const customColumnChange = (settingColumns) => {
    let tempColumns = []
    tempColumns = settingColumns.filter((item) => item.visible === true);
    showColumns.value = [...tempColumns]
  }

  // 币制列表
  const currList = ref([])
  const unitList = ref([])
  const customerList = ref([])
  const getCommonKeyValueList = async () => {
    const res = await window.majesty.httpUtil.postAction(ycCsApi.bizBpAnalyseOrderHead.getCommonKeyValueList,{});
    if (res.code === 200) {
      currList.value = res.data.currList
      unitList.value = res.data.unitList
      customerList.value = res.data.customerList
    }else {
      console.log('获取通用信息失败')
    }
  }


  // 确认数据
  const confirmLoading = ref(false)
  const handleConfirm =  () => {
    // 只能选择一条数据
    if (gridData.selectedRowKeys.length <= 0) {
      message.warning('请选择一条数据')
      return
    }
    if (gridData.selectedRowKeys.length > 1) {
      message.warning('只能选择一条数据')
      return
    }
    // 已经确认的数据不能确认
    if (gridData.selectedData[0].dataState === '1') {
      message.warning('该数据已经确认，无需重复操作')
      return
    }

    let id = gridData.selectedRowKeys[0]
    Modal.confirm({
      title: '提醒?',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '确认',
      cancelText: '取消',
      content: '确认选中数据吗？',
      onOk() {
        confirmLoading.value = true
        window.majesty.httpUtil.postAction(`${ycCsApi.bizBpAnalyseOrderHead.confirm}/${id}`,{}).then(res => {
          if (res.code === 200) {
            message.success("确认成功")
            // 清空选项
            gridData.selectedRowKeys = []
            gridData.selectedData = []
            getList()
          }else {
            message.error(res.message)
          }
        }).catch(err=>{
          console.log(err)
        }).finally(() => {
          confirmLoading.value = false
        })
      },
      onCancel() {

      },
    });
  }


  const handlerBackExtract = () => {
    isShowExtract.value = false
  }
  // 保存提取合同
  const handlerExtract = (data) => {
    // console.log('data',data)
    addLoading.value = true
    // 刷新表格数据
    // handlerSearch()
    window.majesty.httpUtil.postAction(ycCsApi.bizBpAnalyseOrderHead.extractContract,data[0]).then((res)=>{
      // console.log('res',res)

      if (res.code !== 200){
        message.error(res.message)
        return
      }else {
        getList()
        // // 提取完成后，重新刷新数据，
        // Object.assign(formData, res.data.head)
        editConfig.value.editStatus = editStatus.EDIT
        editConfig.value.editData =  res.data
        show.value =!show.value;

      }


    }).finally(()=>{
      addLoading.value = false
      // 关闭提取合同弹框
      isShowExtract.value = false
    })
  }

  /* 打印分析单 */
  const handlePrintAnalysis = () => {
    if (gridData.selectedRowKeys.length <= 0) {
      message.warning('请选择一条数据')
      return
    }
    if (gridData.selectedRowKeys.length > 1) {
      message.warning('只能选择一条数据')
      return
    }

    printAnalysisLoading.value = true
    const params = { id: gridData.selectedRowKeys[0], fileType: 'xlsx' }
    const url = ycCsApi.bizBpAnalyseOrderHead.printAnalysis

    window.majesty.httpUtil.downloadFile(
      url, null, params, 'post', null
    ).then(res => {
      message.success('分析单打印成功')
    }).catch(() => {
      message.error('分析单打印失败')
    }).finally(() => {
      printAnalysisLoading.value = false
    })
  }

  /* 打印装运通知 */
  const handlePrintShipment = () => {
    if (gridData.selectedRowKeys.length <= 0) {
      message.warning('请选择一条数据')
      return
    }
    if (gridData.selectedRowKeys.length > 1) {
      message.warning('只能选择一条数据')
      return
    }

    printShipmentLoading.value = true
    const params = { id: gridData.selectedRowKeys[0], fileType: 'xlsx' }
    const url = ycCsApi.bizBpAnalyseOrderHead.printShipment

    window.majesty.httpUtil.downloadFile(
      url, null, params, 'post', null
    ).then(res => {
      message.success('装运通知打印成功')
    }).catch(() => {
      message.error('装运通知打印失败')
    }).finally(() => {
      printShipmentLoading.value = false
    })
  }

  /* 打印装船电 */
  const handlePrintShipping = () => {
    if (gridData.selectedRowKeys.length <= 0) {
      message.warning('请选择一条数据')
      return
    }
    if (gridData.selectedRowKeys.length > 1) {
      message.warning('只能选择一条数据')
      return
    }

    printShippingLoading.value = true
    const params = { id: gridData.selectedRowKeys[0], fileType: 'xlsx' }
    const url = ycCsApi.bizBpAnalyseOrderHead.printShipping

    window.majesty.httpUtil.downloadFile(
      url, null, params, 'post', null
    ).then(res => {
      message.success('装船电打印成功')
    }).catch(() => {
      message.error('装船电打印失败')
    }).finally(() => {
      printShippingLoading.value = false
    })
  }

  onMounted(fn => {

    getCommonKeyValueList()

    ajaxUrl.selectAllPage = ycCsApi.bizBpAnalyseOrderHead.list
    ajaxUrl.exportUrl = ycCsApi.bizBpAnalyseOrderHead.export

    tableHeight.value = getTableScroll(100, '');

    getList()


  })





</script>

<style lang="less" scoped>


</style>
