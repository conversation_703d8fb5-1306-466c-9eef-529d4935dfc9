<template>
  <section>
    <!-- 第9条线-非国营贸易出口辅料-出货信息表头 -->
    <a-card size="small" title="出货信息表头" class="cs-card-form">
      <div class="cs-form">
        <a-form :loading="fromDataLoading"   ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData"   class=" grid-container">

            <!-- 出货单号 字符型（60） 文本 是 用户录入 唯一性校验 -->
            <a-form-item name="exportNo"   :label="'出货单号'" class="grid-item"  :colon="false">
                <a-input :disabled="showDisable"  size="small" v-model:value="formData.exportNo" allow-clear />
            </a-form-item>
            <!-- 合同号 字符型（60） 文本 是 <新增>操作带出，不允许修改 -->
            <a-form-item name="contractNo"   :label="'合同号'" class="grid-item"  :colon="false">
                <a-input :disabled="true"  size="small" v-model:value="formData.contractNo"  allow-clear />
            </a-form-item>
            <!-- 客户 字符型（200） 下拉框 是 <新增>操作带出-客户，不允许修改 -->
            <a-form-item name="customer" :label="'客户'" class="grid-item"  :colon="false">
              <cs-select
                :disabled="true"
                :options="customerList"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                show-search
                v-model:value="formData.customer"
                id="customer"
              />
            </a-form-item>
            <!-- 客户地址 字符型（60） 文本 是 <新增>操作带出，允许修改 根据客户，关联【客商信息】带出"客商地址" -->
            <a-form-item name="customerAddress"   :label="'客户地址'" class="grid-item"  :colon="false">
                <a-input :disabled="showDisable"  size="small" v-model:value="formData.customerAddress"   allow-clear/>
            </a-form-item>
            <!-- 供应商 字符型（200） 下拉框 是 <新增>操作带出-供应商，不允许修改 -->
            <a-form-item name="supplier"   :label="'供应商'" class="grid-item"  :colon="false">
              <cs-select
                :options="customerList"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                show-search
                :disabled="true"
                v-model:value="formData.supplier"
                id="supplier"
              />
            </a-form-item>
            <!-- 贸易国别 字符型（60） 文本 是 <新增>操作带出，允许修改 根据合同客户，关联【客商信息】带出"贸易国别" -->
            <a-form-item name="tradeCountry"   :label="'贸易国别'" class="grid-item"  :colon="false">
                <a-input :disabled="showDisable"  size="small" v-model:value="formData.tradeCountry"   allow-clear />
            </a-form-item>
            <!-- 经营单位 字符型（200） 下拉框 是 【基础资料-客商信息】 默认"中国烟草上海进出口有限责任公司"允许修改 -->
            <a-form-item name="manageUnit"   :label="'经营单位'" class="grid-item"  :colon="false">
              <cs-select
                :options="customerList"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                show-search
                :disabled="showDisable"
                v-model:value="formData.manageUnit"
                id="manageUnit"
              />
            </a-form-item>
            <!-- 付款方式 字符型（50） 文本 否 <新增>操作带出-收汇方式，不允许修改 -->
            <a-form-item name="paymentType"   :label="'付款方式'" class="grid-item"  :colon="false">
                <a-input :disabled="true"  size="small" v-model:value="formData.paymentType"  allow-clear />
            </a-form-item>
            <!-- 币种 字符型（10） 下拉框 是 <新增>操作带出，不允许修改 -->
            <a-form-item name="currency"   :label="'币种'" class="grid-item"  :colon="false">
              <cs-select
                :options="currList"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                show-search
                :disabled="true"
                v-model:value="formData.currency"
                id="currency"
              />
            </a-form-item>
            <!-- 运输方式 字符型（10） 下拉框 否 默认0海运，允许修改 系统参数0海运1空运2陆运 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="transportType"   :label="'运输方式'" class="grid-item"  :colon="false">
              <cs-select
                :options="productClassify.transportMode"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                show-search
                :disabled="showDisable"
                v-model:value="formData.transportType"
                id="transportType"
              />
            </a-form-item>
            <!-- 价格条款 字符型（20） 下拉框 否 <新增>操作带出，不允许修改 -->
            <a-form-item name="priceTerms"  :label="'价格条款'" class="grid-item"  :colon="false">
              <cs-select
                :options="priceTermList"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                show-search
                :disabled="true"
                v-model:value="formData.priceTerms"
                id="priceTerms"
              />
            </a-form-item>
            <!-- 价格条款对应港口 字符型（50） 下拉框 否 <新增>操作带出，不允许修改 -->
            <a-form-item name="priceTermsPort"   :label="'价格条款对应港口'" class="grid-item"  :colon="false">
              <cs-select
                :options="productClassify.priceTermPort"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                show-search
                :disabled="true"
                v-model:value="formData.priceTermsPort"
                id="priceTermsPort"
              />
            </a-form-item>
            <!-- 发货单位 字符型（60） 文本 是 <新增>操作带出-供应商，不允许修改 -->
            <a-form-item name="deliveryUnit"   :label="'发货单位'" class="grid-item"  :colon="false">
                <a-input :disabled="true"  size="small" v-model:value="formData.deliveryUnit"   allow-clear />
            </a-form-item>
            <!-- 包装种类 字符型（30） 下拉框 否 【企业自定义参数-包装信息】 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="packageType"   :label="'包装种类'" class="grid-item"  :colon="false">
              <cs-select
                :options="packageList"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                show-search
                :disabled="showDisable"
                v-model:value="formData.packageType"
                id="packageType"
              />
            </a-form-item>
            <!-- 包装数量 数值型（10） 文本 否 用户录入 <新增>操作，根据合同号关联<新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="packageNum"   :label="'包装数量'" class="grid-item"  :colon="false">
                <a-input-number :disabled="showDisable"  size="small" v-model:value="formData.packageNum"  style="width: 100%" allow-clear />
            </a-form-item>
            <!-- 发货单位所在地 字符型（50） 下拉框 否 企业自定义参数-城市 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="deliveryUnitLocation"   :label="'发货单位所在地'" class="grid-item"  :colon="false">
              <cs-select
                :options="cityList"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                show-search
                :disabled="showDisable"
                v-model:value="formData.deliveryUnitLocation"
                id="deliveryUnitLocation"
              />
            </a-form-item>
            <!-- 装运人SHIPPER 数值型（300） 文本 否 <新增>操作带出-根据供应商关联【客商信息】-装运人SHIPPER，可修改 -->
            <a-form-item name="shipper"   :label="'装运人SHIPPER'" class="grid-item"  :colon="false">
                <a-input :disabled="showDisable"  size="small" v-model:value="formData.shipper"   allow-clear/>
            </a-form-item>
            <!-- 收货人CONSIGNEE 数值型（300） 文本 否 <新增>操作带出-根据合同客户关联【客商信息】-收货人CONSIGNEE，可修改 -->
            <a-form-item name="consignee"   :label="'收货人CONSIGNEE'" class="grid-item"  :colon="false">
                <a-input :disabled="showDisable"  size="small" v-model:value="formData.consignee"  allow-clear />
            </a-form-item>
            <!-- 通知人NOTIFY PARTY 数值型（300） 文本 否 <新增>操作带出-根据客户关联【客商信息】-通知人NOTIFY PARTY，可修改 -->
            <a-form-item name="notifyParty"   :label="'通知人NOTIFY PARTY'" class="grid-item"  :colon="false">
                <a-input :disabled="showDisable"  size="small" v-model:value="formData.notifyParty"  allow-clear />
            </a-form-item>
            <!-- 总毛重 数值型（19，6） 文本 否 用户录入 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="grossWeight"   :label="'总毛重'" class="grid-item"  :colon="false">
                <a-input-number :disabled="showDisable"  size="small" v-model:value="formData.grossWeight" style="width: 100%"  allow-clear />
            </a-form-item>
            <!-- 总净重 数值型（19，6） 文本 否 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="netWeight"   :label="'总净重'" class="grid-item"  :colon="false">
                <a-input-number :disabled="showDisable"  size="small" v-model:value="formData.netWeight" style="width: 100%" allow-clear />
            </a-form-item>
            <!-- 总皮重 数值型（19，6） 文本 否 系统计算=总毛重-总净重，不允许修改 -->
            <a-form-item name="tareWeight"   :label="'总皮重'" class="grid-item"  :colon="false">
                <a-input-number :disabled="true"  size="small" v-model:value="formData.tareWeight" style="width: 100%" allow-clear />
            </a-form-item>
            <!-- 唛头 数值型（300） 文本 否 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="mark"   :label="'唛头'" class="grid-item"  :colon="false">
                <a-input :disabled="showDisable"  size="small" v-model:value="formData.mark"  allow-clear />
            </a-form-item>
            <!-- 装运港 字符型（50） 下拉框 否 海关参数-港口 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="portOfShipment"   :label="'装运港'" class="grid-item"  :colon="false">
              <cs-select
                :options="portList"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                show-search
                :disabled="showDisable"
                v-model:value="formData.portOfShipment"
                id="portOfShipment"
              />
            </a-form-item>
            <!-- 目的地/港 字符型（50） 下拉框 否 海关参数-港口 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="portOfDestination"   :label="'目的地/港'" class="grid-item"  :colon="false">
              <cs-select
                :options="portList"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                show-search
                :disabled="showDisable"
                v-model:value="formData.portOfDestination"
                id="portOfDestination"
              />
            </a-form-item>
            <!-- 装运期限 日期型（10） 日期控件 否 用户录入 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="shipmentDate"   :label="'装运期限'" class="grid-item"  :colon="false">
              <a-date-picker
                v-model:value="formData.shipmentDate"
                id="shipmentDate"
                size="small"
                valueFormat="YYYY-MM-DD"
                format="YYYY-MM-DD"
                :locale="locale"
                style="width: 100%"
                :disabled="showDisable"
                placeholder=""
              />
            </a-form-item>
            <!-- 险别 字符型（100） 下拉框 否 企业自定义参数-保险类别 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="insuranceType"   :label="'险别'" class="grid-item"  :colon="false">
              <cs-select
                :options="insuranceTypeList"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                :disabled="showDisable"
                show-search
                v-model:value="formData.insuranceType"
                id="insuranceType"
              />
            </a-form-item>
            <!-- 保费币种 字符型（10） 下拉框 否 海关参数自定义，显示为三位英文字母，默认为USD，允许修改 -->
            <a-form-item name="insuranceCurrency"   :label="'保费币种'" class="grid-item"  :colon="false">
              <cs-select
                :options="currList"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                :disabled="showDisable"
                show-search
                v-model:value="formData.insuranceCurrency"
                id="insuranceCurrency"
              />
            </a-form-item>
            <!-- 投保加成% 数值型（19，6） 文本 否 默认1%，允许修改 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="insuranceAddRate"   :label="'投保加成%'" class="grid-item"  :colon="false">
                <a-input-number :disabled="showDisable"  size="small" v-model:value="formData.insuranceAddRate" style="width: 100%"  addon-after="%" allow-clear/>
            </a-form-item>
            <!-- 保费费率(%) 数值型（19，6） 文本 否 默认0.0267%,允许修改 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="insuranceRate"   :label="'保费费率(%)'" class="grid-item"  :colon="false">
                <a-input-number :disabled="showDisable"  size="small" v-model:value="formData.insuranceRate" style="width: 100%"  addon-after="%" allow-clear/>
            </a-form-item>
            <!-- 保险费 数值型（19，6） 文本 否 系统计算=表体金额汇总*（1+投保加成）*保费费率 不允许修改 -->
            <a-form-item name="insuranceFee"   :label="'保险费'" class="grid-item"  :colon="false">
                <a-input-number :disabled="true"  size="small" v-model:value="formData.insuranceFee" style="width: 100%;"  allow-clear />
            </a-form-item>
            <!-- 投保人 字符型（200） 下拉框 是 【基础资料-客商信息】 -->
            <a-form-item name="insurer"   :label="'投保人'" class="grid-item"  :colon="false">
              <cs-select
                :options="customerList"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                show-search
                :disabled="showDisable"
                v-model:value="formData.insurer"
                id="insurer"
              />
            </a-form-item>
            <!-- 运费 数值型（19，6） 文本 否 用户录入 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="freight"   :label="'运费'" class="grid-item"  :colon="false">
                <a-input-number :disabled="showDisable"  size="small" v-model:value="formData.freight" style="width: 100%"  allow-clear/>
            </a-form-item>
            <!-- 运费币种 字符型（10） 下拉框 否 海关参数自定义，显示为三位英文字母，默认为USD，允许修改 <新增>操作，根据合同号关联【合同与协议】分析单TAB带出，允许修改 -->
            <a-form-item name="freightCurrency"   :label="'运费币种'" class="grid-item"  :colon="false">
                <cs-select
                  :options="currList"
                  :combine-display="true"
                  option-filter-prop="label"
                  option-label-prop="key"
                  allow-clear
                  show-search
                  :disabled="showDisable"
                  v-model:value="formData.freightCurrency"
                  id="freightCurrency"
                />
            </a-form-item>
            <!-- 仓储地址 数值型（300） 文本 否 <新增>操作带出-根据委托方关联【客商信息】-仓储地址，可修改 -->
            <a-form-item name="warehouseAddress"   :label="'仓储地址'" class="grid-item"  :colon="false">
                <a-input :disabled="showDisable"  size="small" v-model:value="formData.warehouseAddress"  allow-clear/>
            </a-form-item>
            <!-- 联系人 数值型（20） 文本 否 <新增>操作带出-根据委托方关联【客商信息】-联系人，可修改 -->
            <a-form-item name="contactPerson"   :label="'联系人'" class="grid-item"  :colon="false">
                <a-input :disabled="showDisable"  size="small" v-model:value="formData.contactPerson"  allow-clear/>
            </a-form-item>
            <!-- 联系电话 数值型（20） 文本 否 <新增>操作带出-根据委托方关联【客商信息】-联系电话可修改 -->
            <a-form-item name="contactPhone"   :label="'联系电话'" class="grid-item"  :colon="false">
                <a-input :disabled="showDisable"  size="small" v-model:value="formData.contactPhone"  allow-clear/>
            </a-form-item>

            <!-- 发送报关 字符型（10） 下拉框 是 0是1否 系统带出，不允许修改 数据新增时为1否，操作<发送报关>成功时，置为0是 -->
            <a-form-item name="sendCustoms"   :label="'发送报关'" class="grid-item"  :colon="false">
                <cs-select
                  :options="productClassify.sendEntryStatus"
                  :combine-display="true"
                  option-filter-prop="label"
                  option-label-prop="key"
                  allow-clear
                  show-search
                  :disabled="true"
                  v-model:value="formData.sendCustoms"
                  id="sendCustoms"
                />
            </a-form-item>
            <!-- 确认时间 日期型（18） 日期控件 否 点击"确认"功能按钮，且成功提交的时间：yyyy-mm-dd hh:mm:ss 不允许修改，置灰 -->
            <a-form-item name="confirmTime"   :label="'确认时间'" class="grid-item"  :colon="false">
              <a-date-picker
                v-model:value="formData.confirmTime"
                id="confirmTime"
                size="small"
                valueFormat="YYYY-MM-DD HH:mm:ss"
                format="YYYY-MM-DD HH:mm:ss"
                :locale="locale"
                style="width: 100%"
                placeholder=""
                :disabled="true"
                />
            </a-form-item>

            <!-- 单据状态 -->
            <a-form-item name="dataState"   :label="'单据状态'" class="grid-item"  :colon="false">
              <cs-select
                :options="productClassify.orderStatus"
                :combine-display="true"
                option-filter-prop="label"
                option-label-prop="key"
                allow-clear
                show-search
                v-model:value="formData.dataState"
                id="dataState"
                :disabled="true"
              />
            </a-form-item>
            <!-- 制单人 -->
            <a-form-item name="createBy"   :label="'制单人'" class="grid-item"  :colon="false">
              <a-input :disabled="true"  size="small" v-model:value="formData.createBy"  allow-clear />
            </a-form-item>
            <!-- 制单时间 -->
            <a-form-item name="createTime"   :label="'制单时间'" class="grid-item"  :colon="false">
              <a-date-picker
                v-model:value="formData.createTime"
                id="createTime"
                size="small"
                valueFormat="YYYY-MM-DD HH:mm:ss"
                format="YYYY-MM-DD HH:mm:ss"
                :locale="locale"
                style="width: 100%"
                placeholder=""
                :disabled="true"
              />
            </a-form-item>
<!--            &lt;!&ndash; 准运证编号 字符型（100） 文本 否 用户录入 &ndash;&gt;-->
<!--            <a-form-item name="transportPermitNo"   :label="'准运证编号'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.transportPermitNo" />-->
<!--            </a-form-item>-->
<!--            &lt;!&ndash; 准运证申办日期 日期型（10） 日期控件 否 用户录入 &ndash;&gt;-->
<!--            <a-form-item name="transportPermitApplyDate"   :label="'准运证申办日期'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.transportPermitApplyDate" />-->
<!--            </a-form-item>-->
<!--            &lt;!&ndash; 到货确认日期 日期型（10） 日期控件 否 用户录入 &ndash;&gt;-->
<!--            <a-form-item name="arrivalConfirmDate"   :label="'到货确认日期'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.arrivalConfirmDate" />-->
<!--            </a-form-item>-->

          <!-- 备注 字符型（200） 文本 否 用户录入 -->
          <a-form-item name="remark"   :label="'备注'" class="grid-item merge-3"  :colon="false">
            <a-textarea
              :disabled="showDisable"
              size="small"
              v-model:value="formData.remark"
              :auto-size="{ minRows: 2, maxRows: 3 }"
              allow-clear
            />
          </a-form-item>

          <div class="cs-submit-btn merge-3">
            <a-button size="small"
                      type="primary"
                      :disabled="showDisable"
                      @click="handlerSave" class="cs-margin-right"
                      v-show="props.editConfig.editStatus !==  editStatus.SHOW " :loading="saveLoading">保存
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
            <!-- 确认 -->
            <a-button size="small" type="ghost" @click="handleConfirm" class="cs-margin-right"
                      :loading="confirmOrderLoading"
                      v-show="props.editConfig.editStatus !== editStatus.SHOW ">
              <template #icon>
                <GlobalIcon class="btn-icon" type="check" style="font-size: 12px;"/>
              </template>
              <template #default>
                确认
              </template>
            </a-button>
          </div>
        </a-form>


      </div>
    </a-card>



    <!-- 第9条线-非国营贸易出口辅料-出货信息表体 -->
    <a-card size="small" title="出货信息表体" class="cs-card-form">
      <biz-export-goods-list-list :parentId="props.editConfig.editData.id"
                                  :is-all-confirmed="props.isAllConfirmed"
                                  :is-edit="!showDisable"
                                  :show-disable="props.editConfig.editStatus === editStatus.SHOW"
      />
    </a-card>

  </section>
</template>

<script setup>
    import {editStatus, productClassify} from '@/view/common/constant'
    import {message, Modal} from "ant-design-vue";
    import {createVNode, onMounted, reactive, ref, watch} from "vue";
    import CsSelect from "@/components/select/CsSelect.vue";
    import {usePCode} from "@/view/common/usePCode";
    import ycCsApi from "@/api/ycCsApi";
    import useEventBus from "@/view/common/eventBus";
    import BizExportGoodsListList from "@/view/dec/export/list/BizExportGoodsListList.vue";
    import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
    const { getPCode } = usePCode()
    const { emitEvent } = useEventBus()



    const props = defineProps({
      editConfig: {
        type: Object,
        default: () => {
        }
      },
      isAllConfirmed:{
        type: Boolean,
        default: false
      }
    });

    // 定义子组件 emit事件，用于子组件向父组件传递数据
    const emit = defineEmits(['onBack']);

    const onBack = (val) => {
      emit('onBack', val);
    };

    // 是否禁用
    const showDisable = ref(false)

    // 表单数据
    const formData = reactive({
        // 主键id
        id:'',
        // 业务类型
        businessType:'',
        // 数据状态
        dataState:'',
        // 版本号
        versionNo:'',
        // 企业10位编码
        tradeCode:'',
        // 组织机构代码
        sysOrgCode:'',
        // 父级id
        parentId:'',
        // 创建人
        createBy:'',
        // 创建时间
        createTime:'',
        createTimeTo:'',
        createTimeForm:'',
        // 更新人
        updateBy:'',
        // 更新时间
        updateTime:'',
        updateTimeTo:'',
        updateTimeForm:'',
        // 插入用户名
        insertUserName:'',
        // 更新用户名
        updateUserName:'',
        // 扩展字段1
        extend1:'',
        // 扩展字段2
        extend2:'',
        // 扩展字段3
        extend3:'',
        // 扩展字段4
        extend4:'',
        // 扩展字段5
        extend5:'',
        // 扩展字段6
        extend6:'',
        // 扩展字段7
        extend7:'',
        // 扩展字段8
        extend8:'',
        // 扩展字段9
        extend9:'',
        // 扩展字段10
        extend10:'',
        // 出货单号
        exportNo:'',
        // 合同号
        contractNo:'',
        // 客户
        customer:'',
        // 客户地址
        customerAddress:'',
        // 供应商
        supplier:'',
        // 贸易国别
        tradeCountry:'',
        // 经营单位
        manageUnit:'',
        // 付款方式
        paymentType:'',
        // 币种
        currency:'',
        // 运输方式
        transportType:'',
        // 价格条款
        priceTerms:'',
        // 价格条款对应港口
        priceTermsPort:'',
        // 发货单位
        deliveryUnit:'',
        // 包装种类
        packageType:'',
        // 包装数量
        packageNum:'',
        // 发货单位所在地
        deliveryUnitLocation:'',
        // 装运人shipper
        shipper:'',
        // 收货人consignee
        consignee:'',
        // 通知人notify party
        notifyParty:'',
        // 总毛重
        grossWeight:'',
        // 总净重
        netWeight:'',
        // 总皮重
        tareWeight:'',
        // 唛头
        mark:'',
        // 装运港
        portOfShipment:'',
        // 目的地/港
        portOfDestination:'',
        // 装运期限
        shipmentDate:'',
        // 险别
        insuranceType:'',
        // 保费币种
        insuranceCurrency:'',
        // 投保加成%
        insuranceAddRate:'',
        // 保费费率(%)
        insuranceRate:'',
        // 保险费
        insuranceFee:'',
        // 投保人
        insurer:'',
        // 运费
        freight:'',
        // 运费币种
        freightCurrency:'',
        // 仓储地址
        warehouseAddress:'',
        // 联系人
        contactPerson:'',
        // 联系电话
        contactPhone:'',
        // 备注
        remark:'',
        // 发送报关
        sendCustoms:'',
        // 确认时间
        confirmTime:'',
        confirmTimeTo:'',
        confirmTimeForm:'',
        // 准运证编号，用户录入
        transportPermitNo:'',
        // 准运证申办日期，用户录入
        transportPermitApplyDate:'',
        transportPermitApplyDateTo:'',
        transportPermitApplyDateForm:'',
        // 到货确认日期，用户录入
        arrivalConfirmDate:'',
        arrivalConfirmDateTo:'',
        arrivalConfirmDateForm:''
    })
    /**
     * 校验出货单号是否唯一
     * @param rule 校验规则
     * @param value 校验值
     * @param callback 回调函数
     */
    const checkExportNo = async (rule, value) => {
      return new Promise(async (resolve, reject) => {
        try {
          const res = await window.majesty.httpUtil.postAction(
            ycCsApi.bizExportGoodsHead.checkExportNo,
            formData
          );

          if (res.code === 200) {
            if (res && res.data && res.data === 999) {
              reject(res.message);
            } else if (res.data > 0) {
              reject('出货单号已经存在！');
            } else {
              resolve(); // 校验通过
            }
          } else {
            resolve(); // 或根据业务逻辑决定是否 reject
          }
        } catch (error) {
          console.error("校验请求失败:", error);
          reject("网络异常，请重试");
        }
      });
    };

    // 校验规则
    const rules = {
        id:[
        ],
        businessType:[
            {max: 120, message: '业务类型长度不能超过 120位字节', trigger: 'blur'}
        ],
        dataState:[
            {required: true, message: '单据状态不能为空', trigger: 'blur'},
            {max: 20, message: '单据状态长度不能超过 20位字节', trigger: 'blur'}
        ],
        versionNo:[
            {max: 20, message: '版本号长度不能超过 20位字节', trigger: 'blur'}
        ],
        tradeCode:[
            {max: 20, message: '企业10位编码长度不能超过 20位字节', trigger: 'blur'}
        ],
        sysOrgCode:[
            {max: 20, message: '组织机构代码长度不能超过 20位字节', trigger: 'blur'}
        ],
        parentId:[
        ],
        createBy:[
          {required: true, message: '制单人不能为空', trigger: 'blur'},
          {max: 100, message: '创建人长度不能超过 100位字节', trigger: 'blur'}
        ],
        createTime:[
          {required: true, message: '制单时间不能为空', trigger: 'blur'},
        ],
        updateBy:[
            {max: 100, message: '更新人长度不能超过 100位字节', trigger: 'blur'}
        ],
        updateTime:[
        ],
        insertUserName:[
            {max: 100, message: '插入用户名长度不能超过 100位字节', trigger: 'blur'}
        ],
        updateUserName:[
            {max: 100, message: '更新用户名长度不能超过 100位字节', trigger: 'blur'}
        ],
        extend1:[
            {max: 400, message: '扩展字段1长度不能超过 400位字节', trigger: 'blur'}
        ],
        extend2:[
            {max: 400, message: '扩展字段2长度不能超过 400位字节', trigger: 'blur'}
        ],
        extend3:[
            {max: 400, message: '扩展字段3长度不能超过 400位字节', trigger: 'blur'}
        ],
        extend4:[
            {max: 400, message: '扩展字段4长度不能超过 400位字节', trigger: 'blur'}
        ],
        extend5:[
            {max: 400, message: '扩展字段5长度不能超过 400位字节', trigger: 'blur'}
        ],
        extend6:[
            {max: 400, message: '扩展字段6长度不能超过 400位字节', trigger: 'blur'}
        ],
        extend7:[
            {max: 400, message: '扩展字段7长度不能超过 400位字节', trigger: 'blur'}
        ],
        extend8:[
            {max: 400, message: '扩展字段8长度不能超过 400位字节', trigger: 'blur'}
        ],
        extend9:[
            {max: 400, message: '扩展字段9长度不能超过 400位字节', trigger: 'blur'}
        ],
        extend10:[
            {max: 400, message: '扩展字段10长度不能超过 400位字节', trigger: 'blur'}
        ],
        exportNo:[
            {required: true, message: '出货单号不能为空', trigger: 'blur'},
            {max: 60, message: '出货单号长度不能超过 60位字节', trigger: 'blur'},
            {validator: checkExportNo, trigger: 'blur'}
        ],
        contractNo:[
            {required: true, message: '合同号不能为空', trigger: 'blur'},
            {max: 60, message: '合同号长度不能超过 60位字节', trigger: 'blur'}
        ],
        customer:[
            {required: true, message: '客户不能为空', trigger: 'blur'},
            {max: 200, message: '客户长度不能超过 200位字节', trigger: 'blur'}
        ],
        customerAddress:[
            {required: true, message: '客户地址不能为空', trigger: 'blur'},
            {max: 60, message: '客户地址长度不能超过 60位字节', trigger: 'blur'}
        ],
        supplier:[
            {required: true, message: '供应商不能为空', trigger: 'blur'},
            {max: 200, message: '供应商长度不能超过 200位字节', trigger: 'blur'}
        ],
        tradeCountry:[
            {required: true, message: '贸易国别不能为空', trigger: 'blur'},
            {max: 60, message: '贸易国别长度不能超过 60位字节', trigger: 'blur'}
        ],
        manageUnit:[
            {required: true, message: '经营单位不能为空', trigger: 'blur'},
            {max: 200, message: '经营单位长度不能超过 200位字节', trigger: 'blur'}
        ],
        paymentType:[
            {max: 50, message: '付款方式长度不能超过 50位字节', trigger: 'blur'}
        ],
        currency:[
            {required: true, message: '币种不能为空', trigger: 'blur'},
            {max: 10, message: '币种长度不能超过 10位字节', trigger: 'blur'}
        ],
        transportType:[
            {max: 10, message: '运输方式长度不能超过 10位字节', trigger: 'blur'}
        ],
        priceTerms:[
            {max: 20, message: '价格条款长度不能超过 20位字节', trigger: 'blur'}
        ],
        priceTermsPort:[
            {max: 50, message: '价格条款对应港口长度不能超过 50位字节', trigger: 'blur'}
        ],
        deliveryUnit:[
            {required: true, message: '发货单位不能为空', trigger: 'blur'},
            {max: 60, message: '发货单位长度不能超过 60位字节', trigger: 'blur'}
        ],
        packageType:[
            {max: 30, message: '包装种类长度不能超过 30位字节', trigger: 'blur'}
        ],
        packageNum:[

        ],
        deliveryUnitLocation:[
            {max: 50, message: '发货单位所在地长度不能超过 50位字节', trigger: 'blur'}
        ],
        shipper:[
            {max: 300, message: '装运人shipper长度不能超过 300位字节', trigger: 'blur'}
        ],
        consignee:[
            {max: 300, message: '收货人consignee长度不能超过 300位字节', trigger: 'blur'}
        ],
        notifyParty:[
            {max: 300, message: '通知人notify party长度不能超过 300位字节', trigger: 'blur'}
        ],
        grossWeight:[

        ],
        netWeight:[

        ],
        tareWeight:[

        ],
        mark:[
            {max: 300, message: '唛头长度不能超过 300位字节', trigger: 'blur'}
        ],
        portOfShipment:[
            {max: 50, message: '装运港长度不能超过 50位字节', trigger: 'blur'}
        ],
        portOfDestination:[
            {max: 50, message: '目的地/港长度不能超过 50位字节', trigger: 'blur'}
        ],
        shipmentDate:[
        ],
        insuranceType:[
            {max: 100, message: '险别长度不能超过 100位字节', trigger: 'blur'}
        ],
        insuranceCurrency:[
            {max: 10, message: '保费币种长度不能超过 10位字节', trigger: 'blur'}
        ],
        insuranceAddRate:[

        ],
        insuranceRate:[

        ],
        insuranceFee:[

        ],
        insurer:[
            {required: true, message: '投保人不能为空', trigger: 'blur'},
            {max: 200, message: '投保人长度不能超过 200位字节', trigger: 'blur'}
        ],
        freight:[

        ],
        freightCurrency:[
            {max: 10, message: '运费币种长度不能超过 10位字节', trigger: 'blur'}
        ],
        warehouseAddress:[
            {max: 300, message: '仓储地址长度不能超过 300位字节', trigger: 'blur'}
        ],
        contactPerson:[
            {max: 20, message: '联系人长度不能超过 20位字节', trigger: 'blur'}
        ],
        contactPhone:[
            {max: 20, message: '联系电话长度不能超过 20位字节', trigger: 'blur'}
        ],
        remark:[
            {max: 200, message: '备注长度不能超过 200位字节', trigger: 'blur'}
        ],
        sendCustoms:[
            {required: true, message: '发送报关不能为空', trigger: 'blur'},
            {max: 10, message: '发送报关长度不能超过 10位字节', trigger: 'blur'}
        ],
        confirmTime:[
        ],
        transportPermitNo:[
            {max: 100, message: '准运证编号，用户录入长度不能超过 100位字节', trigger: 'blur'}
        ],
        transportPermitApplyDate:[
        ],
        arrivalConfirmDate:[
        ]
    }



    const pCode = ref('')

    const customerList = ref([]);
    const currList = ref([]);
    const priceTermList = ref([]);
    const packageList = ref([]);
    const cityList = ref([]);
    const insuranceTypeList = ref([]);
    const portList = ref([]);
    const fromDataLoading = ref(false);
    const getCommonKeyValueList = async () => {
      fromDataLoading.value = true;
      try {
        const res = await window.majesty.httpUtil.postAction(ycCsApi.bizExportGoodsHead.getCommonKeyValueList,{});
        if (res.code === 200) {
          customerList.value = res.data.customerList;
          currList.value = res.data.currList;
          priceTermList.value = res.data.priceTermList;
          packageList.value = res.data.packageList;
          cityList.value = res.data.cityList;
          insuranceTypeList.value = res.data.insuranceTypeList;
          portList.value = res.data.portList;
        }else {
          message.error(res.message);
        }
      }catch(err) {
        console.log(err);
      }finally {
        fromDataLoading.value = false;
      }

    }

    /* 确认 */
    const confirmOrderLoading = ref(false);
    const handleConfirm = ()=> {
      if (formData.dataState === '2') {
        message.warning('该数据已作废，不允许进行确认操作！')
        return
      }

      if (formData.dataState === '1') {
        message.warning('该数据已经确认，无需重复操作！')
        return
      }

      // 弹出确认框
      Modal.confirm({
        title: '提醒?',
        icon: createVNode(ExclamationCircleOutlined),
        okText: '确认',
        cancelText: '取消',
        content: '是否确认所选项？',
        onOk() {
          confirmOrderLoading.value = true
          window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsHead.confirm}`,formData).then(res=>{
            if (res.code === 200) {
              message.success("确认成功！")
              Object.assign(formData, res.data)
              onBack({
                editData: res.data,
                editStatus: editStatus.EDIT
              })
            }else {
              message.error(res.message)
            }
          }).finally(() => {
            confirmOrderLoading.value = false
          })
          // confirmIOrderHead(formData.value).then(res => {
          //   if (res.code === 200) {
          //     message.success("确认成功！")
          //     Object.assign(formData.value, res.data)
          //     onBack({
          //       editData: res.data,
          //       showBodyPurchaseHead: true,
          //       showBody: true,
          //       editStatus: editStatus.EDIT
          //     })
          //   }else {
          //     message.error(res.message)
          //   }
          // }).finally(() => {
          //   confirmOrderLoading.value = false
          // })
        },
        onCancel() {
          confirmOrderLoading.value = false
        },
      });
    }

    // 初始化操作
    onMounted(() => {
      getCommonKeyValueList();
      getPCode().then(res=>{
        console.log('res',res)
        pCode.value = res;
      })
      // 初始化数据
      if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
        Object.assign(formData, props.editConfig.editData);
        showDisable.value = false
      }
      if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
        Object.assign(formData, props.editConfig.editData);
        showDisable.value = true
      }
      
      // 设置经营单位默认值
      if (!formData.manageUnit) {
        formData.manageUnit = '中国烟草上海进出口有限责任公司';
      }
      
      // 设置运输方式默认值（0海运）
      if (!formData.transportType) {
        formData.transportType = '0';
      }
      
      // 设置保费币种默认值（USD）
      if (!formData.insuranceCurrency) {
        formData.insuranceCurrency = 'USD';
      }
      
      // 设置运费币种默认值（USD）
      if (!formData.freightCurrency) {
        formData.freightCurrency = 'USD';
      }
      
      // 设置投保加成默认值（1%）
      if (!formData.insuranceAddRate) {
        formData.insuranceAddRate = 1;
      }
      
      // 设置保费费率默认值（0.0267%）
      if (!formData.insuranceRate) {
        formData.insuranceRate = 0.0267;
      }
      
      // 设置发送报关默认值（1否）
      if (!formData.sendCustoms) {
        formData.sendCustoms = '1';
      }
      
      // 设置单据状态默认值（0编制）
      if (!formData.dataState) {
        formData.dataState = '0';
      }
      
      // 设置制单时间默认值（当前时间）
      if (!formData.createTime) {
        const now = new Date();
        formData.createTime = now.toISOString().slice(0, 19).replace('T', ' ');
      }
      
      // 设置制单人默认值（当前用户）
      if (!formData.createBy) {
        // 从系统获取当前用户信息
        const currentUser = window.majesty?.userInfo?.username || 'system';
        formData.createBy = currentUser;
      }
    });



    // vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
    const formRef = ref(null);
    const saveLoading = ref(false);
    // 保存
    const handlerSave = () => {
      formRef.value
        .validate()
        .then(() => {
          saveLoading.value = true;
          // 插入数据
          window.majesty.httpUtil.putAction(`${ycCsApi.bizExportGoodsHead.update}/${formData.id}`,formData).then(res=>{
            if (res.code === 200) {
              message.success('修改成功')
              // 重新赋值当前数据
              Object.assign(formData, res.data);
              // 重新赋值下拉框数据
              emitEvent('refresh-export-goods-head-search')
            }else {
              message.error(res.message);
            }
          }).finally(() => {
            saveLoading.value = false;
          })


          // 更新数据
          // window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsHead.update}/${formData.id}`, formData).then((res)=>{
          //   if (res.code === 200){
          //     message.success('修改成功!')
          //     onBack(true)
          //   }
          // }).catch(error => {
          //   console.log('validate failed', error);
          // })
        })
        .catch(error => {
          console.log('validate failed', error);
        })
    };


    /* 监控 grossWeight（总毛重） 、 netWeight（总净重） */
    watch(()=>[formData.grossWeight,formData.netWeight],([grossWeight,netWeight])=>{
      if (grossWeight && netWeight) {
        // 总皮重 = 总毛重 - 总净重
        formData.tareWeight = grossWeight - netWeight;
      }
    })


    /* 监控formData中，dataState的变化 */
    watch(()=>formData.dataState, (newVal,oldVal)=>{
      if (newVal === '1') {
        showDisable.value = true
      }
    })


</script>

<style lang="less" scoped>


</style>



