<template>
  <section>
    <!-- 操作按钮区域 -->
    <div class="cs-action-btn">
      <div class="cs-action-btn-item">
        <a-button size="small" @click="handleAdd">
          <template #icon>
            <GlobalIcon type="plus" style="color:green"/>
          </template>
          新增
        </a-button>
      </div>
      <div class="cs-action-btn-item">
        <a-button size="small" @click="handleDelete" :disabled="selectedRowKeys.length === 0">
          <template #icon>
            <GlobalIcon type="delete" style="color:red"/>
          </template>
          删除
        </a-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <a-table
      ref="tableRef"
      class="cs-action-item"
      size="small"
      :scroll="{ y: 400, x: 800 }"
      bordered
      :pagination="false"
      :columns="tableColumns"
      :data-source="tableData"
      :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      row-key="id"
      :loading="tableLoading"
    >
      <!-- 行内编辑模板 -->
      <template #bodyCell="{ text, record, index, column }">
        <template v-if="column.dataIndex === 'containerType'">
          <div>
            <a-select
              v-model:value="record[column.dataIndex]"
              size="small"
              style="width: 100%"
              placeholder="请选择箱型"
              show-search
              :filter-option="filterOption"
              @change="handleContainerTypeChange(record.id, $event)"
            >
              <a-select-option v-for="item in containerOptions" :key="item" :value="item">
                {{ item }}
              </a-select-option>
            </a-select>
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'unitPriceTotal'">
          <div>
            <a-input-number
              v-model:value="record[column.dataIndex]"
              size="small"
              style="width: 100%"
              :precision="6"
              :formatter="value => value ? `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
              :parser="value => value.replace(/\$\s?|(,*)/g, '')"
              :disabled="true"
              placeholder="选择箱型后自动填充"
            />
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'containerCount'">
          <div>
            <a-input-number
              v-model:value="record[column.dataIndex]"
              size="small"
              style="width: 100%"
              :precision="6"
              :formatter="value => value ? `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
              :parser="value => value.replace(/\$\s?|(,*)/g, '')"
              @blur="handleAmountCalculation(record)"
              placeholder="请输入箱数"
            />
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'amount'">
          <div>
            <a-input-number
              v-model:value="record[column.dataIndex]"
              size="small"
              style="width: 100%"
              :precision="2"
              :formatter="value => value ? `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
              :parser="value => value.replace(/\$\s?|(,*)/g, '')"
              :disabled="true"
              placeholder="自动计算"
            />
          </div>
        </template>
      </template>
    </a-table>

    <!-- 调试信息 -->

    <!-- 合计行 -->
    <div class="total-row">
      <span>合计：{{ formatNumber(totalAmount, 2) }}</span>
    </div>

    <!-- 底部按钮 -->
    <div class="cs-form-action">
      <a-button type="primary" @click="handleSave" :loading="saveLoading">保存</a-button>
      <a-button @click="handleReturn">返回</a-button>
    </div>
  </section>
</template>

<script setup>
import { computed, onMounted, reactive, ref, nextTick } from "vue";
import { message, Modal } from "ant-design-vue";
import {
  getEquipmentContainerInfoList,
  insertEquipmentContainerInfo,
  updateEquipmentContainerInfo,
  deleteEquipmentContainerInfo,
  getContainerList,
  getContainerMes, updateAllEquipmentContainerInfo
} from "@/api/equipment/equipmentPlanApi";
import { useColumnsRender } from "@/view/common/useColumnsRender";
import { deepClone } from "@/view/utils/common";

defineOptions({
  name: 'ContainerDetailModal'
});

const props = defineProps({
  headId: {
    type: String,
    required: true
  },
  businessType: {
    type: String,
    default: '3' // 默认为3-国营贸易进口烟机设备
  }
});

const emit = defineEmits(['cancel', 'save-success']);

const { formatNumber } = useColumnsRender();

// 表格相关状态
const tableRef = ref();
const tableData = ref([]);
const selectedRowKeys = ref([]);
const tableLoading = ref(false);
const saveLoading = ref(false);

// 箱型选项
const containerOptions = ref([]);

// 数据是否已修改标记
const isDataModified = ref(false);

// 原始数据备份（用于比较是否有修改）
const originalData = ref([]);

// 表格列定义
const tableColumns = [
  {
    title: '箱型',
    dataIndex: 'containerType',
    width: 200,
    align: 'center'
  },
  {
    title: '单价合计',
    dataIndex: 'unitPriceTotal',
    width: 150,
    align: 'center'
  },
  {
    title: '箱数',
    dataIndex: 'containerCount',
    width: 120,
    align: 'center'
  },
  {
    title: '金额',
    dataIndex: 'amount',
    width: 150,
    align: 'center'
  }
];

// 计算合计金额
const totalAmount = computed(() => {
  return tableData.value.reduce((sum, item) => {
    return sum + (parseFloat(item.amount) || 0);
  }, 0);
});



// 过滤选项
const filterOption = (input, option) => {
  return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 箱型变更处理
const handleContainerTypeChange = async (id, containerType) => {
  try {
    if (!containerType) return;

    // 调用获取单价合计信息接口
    const result = await getContainerMes(props.businessType, containerType);
    if (result.code === 200 && result.data) {
      // 找到对应的记录并更新单价合计
      const record = tableData.value.find(item => item.id === id);
      if (record) {
        record.unitPriceTotal = result.data;
        // 重新计算金额
        handleAmountCalculation(record);
        // 标记数据已修改
        isDataModified.value = true;
      }
    } else {
      message.error(result.message || '获取单价合计信息失败');
    }
  } catch (error) {
    console.error('获取单价合计信息失败:', error);
    message.error('获取单价合计信息失败');
  }
};

// 金额计算
const handleAmountCalculation = (record) => {
  if (record && record.unitPriceTotal && record.containerCount) {
    record.amount = parseFloat((record.unitPriceTotal * record.containerCount).toFixed(2));
  }
  // 标记数据已修改
  isDataModified.value = true;
};

// 表格行选择
const onSelectChange = (keys) => {
  selectedRowKeys.value = keys;
};

// 检查数据是否有修改
const checkDataModified = () => {
  // 简单的数据修改检查
  if (tableData.value.length !== originalData.value.length) {
    isDataModified.value = true;
    return;
  }

  // 检查每行数据是否有变化
  for (let i = 0; i < tableData.value.length; i++) {
    const current = tableData.value[i];
    const original = originalData.value[i];

    if (!original ||
        current.containerType !== original.containerType ||
        current.containerCount !== original.containerCount ||
        current.unitPriceTotal !== original.unitPriceTotal) {
      isDataModified.value = true;
      return;
    }
  }

  isDataModified.value = false;
};

// 新增行
const handleAdd = () => {
  const newId = Date.now().toString() + '_add';
  const newRecord = {
    id: newId,
    headId: props.headId,
    containerType: '',
    unitPriceTotal: null,
    containerCount: null,
    amount: null
  };

  // 直接添加到表格数据
  tableData.value = [
    ...tableData.value,
    newRecord
  ];

  // 标记数据已修改
  isDataModified.value = true;
};

// 删除行
const handleDelete = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的数据');
    return;
  }

  try {
    // 分离需要调用API删除的数据和本地删除的数据
    const toDeleteFromApi = [];
    const toDeleteLocally = [];

    selectedRowKeys.value.forEach(id => {
      if (id.toString().includes('add')) {
        // 新增的数据，只需本地删除
        toDeleteLocally.push(id);
      } else {
        // 已存在的数据，需要调用API删除
        toDeleteFromApi.push(id);
      }
    });

    // 调用API删除已存在的数据
    if (toDeleteFromApi.length > 0) {
      await deleteEquipmentContainerInfo(toDeleteFromApi.join(','));
    }

    // 从表格中移除所有选中的数据
    tableData.value = tableData.value.filter(item => !selectedRowKeys.value.includes(item.id));
    selectedRowKeys.value = [];

    // 标记数据已修改
    isDataModified.value = true;
    message.success('删除成功');
  } catch (error) {
    console.error('删除失败:', error);
    message.error('删除失败');
  }
};

// 汇总箱型数据
const summarizeContainerData = () => {
  const containerMap = new Map();

  // 按箱型汇总数量
  tableData.value.forEach(item => {
    if (item.containerType && item.containerCount) {
      const existingCount = containerMap.get(item.containerType) || 0;
      containerMap.set(item.containerType, existingCount + parseFloat(item.containerCount));
    }
  });

  // 生成显示文本
  const displayParts = [];
  let totalCount = 0;

  containerMap.forEach((count, containerType) => {
    displayParts.push(`${containerType}*${count}`);
    totalCount += count;
  });

  return {
    displayText: displayParts.join('+'),
    totalCount: totalCount,
    containerMap: containerMap
  };
};

// 保存所有数据
const handleSave = async () => {
  try {
    saveLoading.value = true;

    // 验证所有行数据
    for (const item of tableData.value) {
      if (!item.containerType) {
        message.error('箱型不能为空');
        return;
      }
      if (!item.unitPriceTotal) {
        message.error('单价合计不能为空');
        return;
      }
      if (!item.containerCount) {
        message.error('箱数不能为空');
        return;
      }
    }

    // 批量保存数据
    const savePromises = tableData.value.map(item => {
      const saveData = {
        ...item,
        headId: props.headId
      };
      if (item.id && !item.id.toString().includes('add')) {
        // 更新现有数据
        return updateEquipmentContainerInfo(item.id, saveData);
      } else {
        // 新增数据
        delete saveData.id; // 移除临时ID
        return insertEquipmentContainerInfo(saveData);
      }
    });

    await Promise.all(savePromises);
    message.success('保存成功');

    // 汇总数据并传递给父组件
    const containerSummary = summarizeContainerData();
    emit('save-success', containerSummary);
  } catch (error) {
    message.error('保存失败');
  } finally {
    saveLoading.value = false;
  }
};

// 返回
const handleReturn = () => {
  // 检查是否有未保存的修改
  if (isDataModified.value) {
    Modal.confirm({
      title: '提示',
      content: '数据未保存，确定要返回吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        // 如果确定返回，删除未保存的箱型数据
        try {
          // 获取所有新增的数据ID（包含'add'的临时ID）
          const newAddedIds = tableData.value
            .filter(item => item.id && item.id.toString().includes('add'))
            .map(item => item.id);

          // 如果有新增的数据，不需要调用删除API，因为它们还没有保存到后端
          // 只需要调用删除API删除headId相关的所有数据
          if (tableData.value.length > originalData.value.length ||
              tableData.value.some(item => !item.id || item.id.toString().includes('add'))) {
            await deleteEquipmentContainerInfo(props.headId);
          }
        } catch (error) {
          console.error('删除箱型数据失败:', error);
        }
        emit('cancel');
      }
    });
  } else {
    emit('cancel');
  }
};

// 获取箱型选项
const loadContainerOptions = async () => {
  try {
    console.log('开始获取箱单列表，businessType:', props.businessType);
    const result = await getContainerList(props.businessType);
    console.log('获取箱单列表接口返回结果:', result);

    if (result.code === 200) {
      // 确保数据格式正确
      containerOptions.value = result.data || [];
      console.log('箱型选项数据已设置:', containerOptions.value);
    } else {
      console.warn('获取箱单列表失败:', result.message);
      containerOptions.value = [];
    }
  } catch (error) {
    console.error('获取箱单列表失败:', error);
    containerOptions.value = [];
  }
};

// 加载表格数据
const loadTableData = async () => {
  try {
    tableLoading.value = true;
    const params = {
      headId: props.headId,
      page: 1,
      limit: 1000 // 获取所有数据
    };

    const result = await getEquipmentContainerInfoList(params);
    if (result.code === 200) {
      // 确保每行数据都有唯一的ID
      tableData.value = (result.data || []).map(item => ({
        ...item,
        // id: item.id || item.sid || Date.now() + Math.random()
      }));

      // 备份原始数据
      originalData.value = JSON.parse(JSON.stringify(tableData.value));
      isDataModified.value = false;
    } else {
      message.error(result.message || '获取数据失败');
      tableData.value = [];
      originalData.value = [];
    }
  } catch (error) {
    message.error('获取数据失败');
    tableData.value = [];
  } finally {
    tableLoading.value = false;
  }
};

// 组件挂载时初始化
onMounted(async () => {
  await loadContainerOptions();
  await loadTableData();
  console.log(props.headId)
});
</script>

<style lang="less" scoped>
.cs-action-btn {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.total-row {
  margin: 16px 0;
  text-align: right;
  font-weight: bold;
  font-size: 14px;
}

.cs-form-action {
  margin-top: 16px;
  text-align: center;
}

.cs-form-action .ant-btn {
  margin: 0 8px;
}
</style>
